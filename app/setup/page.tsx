"use client";

import { useRouter } from "next/navigation";
import RoutineProcessor from "@/app/components/RoutineProcessor";
import ThemeToggle from "@/app/components/ui/theme-toggle";

export default function SetupPage() {
  const router = useRouter();

  return (
    <div className="min-h-screen bg-gradient-to-br from-background to-background/95 transition-colors duration-300">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <header className="text-center mb-8">
          <div className="flex justify-between items-center mb-6">
            <button
              onClick={() => router.push("/")}
              className="flex items-center gap-2 text-muted-foreground hover:text-foreground transition-colors duration-200 group"
            >
              <svg 
                xmlns="http://www.w3.org/2000/svg" 
                fill="none" 
                viewBox="0 0 24 24" 
                strokeWidth={1.5} 
                stroke="currentColor" 
                className="w-5 h-5 group-hover:-translate-x-1 transition-transform duration-200"
              >
                <path strokeLinecap="round" strokeLinejoin="round" d="M10.5 19.5L3 12m0 0l7.5-7.5M3 12h18" />
              </svg>
              Back to Routine Viewer
            </button>
            <ThemeToggle />
          </div>

          <h1 className="text-3xl sm:text-5xl font-extrabold tracking-tight mb-4 animate-fade-in">
            <span className="bg-clip-text text-transparent bg-gradient-to-r from-primary to-purple-600">
              <span className="inline-block animate-bounce-subtle mr-2">⚙️</span>
              Routine Setup
            </span>
          </h1>
          
          <div className="max-w-3xl mx-auto mb-8 animate-fade-in">
            <p className="text-muted-foreground text-lg mb-4">
              Upload your Excel routine file to convert it into a structured format for the class routine viewer.
            </p>
            <div className="bg-card/50 border border-border/50 rounded-lg p-4 text-sm text-muted-foreground">
              <p className="mb-2">
                <strong className="text-primary">How it works:</strong>
              </p>
              <ol className="list-decimal list-inside space-y-1 text-left">
                <li>Upload your Excel routine file (.xlsx format)</li>
                <li>The system will parse and transform the data</li>
                <li>Download the processed Excel file</li>
                <li>Use the processed file in your routine viewer</li>
              </ol>
            </div>
          </div>
        </header>

        {/* Main Content */}
        <div className="max-w-4xl mx-auto">
          <RoutineProcessor />
        </div>
      </div>
    </div>
  );
}
