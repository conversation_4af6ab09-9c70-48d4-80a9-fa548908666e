"use client";

import { useState, useEffect, useMemo } from "react";
import { useRouter } from "next/navigation";
import SearchInput from "@/app/components/ui/search-input";
import LoadingSpinner from "@/app/components/ui/loading-spinner";
import DaySelector from "@/app/components/ui/day-selector";
import ClassList from "@/app/components/class-list";
import EmptyState from "@/app/components/ui/empty-state";
import ThemeToggle from "@/app/components/ui/theme-toggle";
import {
  fetchRoutineData,
  findClassesForSection,
  getCurrentDay,
  getNextSixDays,
  getLastSection,
  saveLastSection
} from "@/app/lib/utils";
import { GroupedClasses } from "@/app/lib/types";
import { DayInfo } from "@/app/lib/types";
import JsonLd from '@/app/components/json-ld';
import { generateWebsiteSchema, generateEducationalAppSchema } from '@/app/lib/structured-data';

export default function Home() {
  const router = useRouter();
  const [sectionId, setSectionId] = useState<string>("");
  const [selectedDay, setSelectedDay] = useState<string>("");
  const [classData, setClassData] = useState<GroupedClasses>({});
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isSearching, setIsSearching] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [days, setDays] = useState<DayInfo[]>([]);
  const [prevSelectedDay, setPrevSelectedDay] = useState<string>("");
  const [isTransitioning, setIsTransitioning] = useState<boolean>(false);
  const [showMobileInfo, setShowMobileInfo] = useState<boolean>(false);
  const [isAppLoading, setIsAppLoading] = useState<boolean>(true);
  const [isOffline, setIsOffline] = useState<boolean>(false);

  // Register service worker for offline support
  useEffect(() => {
    if ('serviceWorker' in navigator) {
      window.addEventListener('load', () => {
        navigator.serviceWorker.register('/service-worker.js')
          .then(registration => {
            console.log('Service Worker registered with scope:', registration.scope);
          })
          .catch(error => {
            console.error('Service Worker registration failed:', error);
          });
      });
    }

    // Check and update online/offline status
    const handleOnlineStatusChange = () => {
      setIsOffline(!navigator.onLine);
    };

    window.addEventListener('online', handleOnlineStatusChange);
    window.addEventListener('offline', handleOnlineStatusChange);
    setIsOffline(!navigator.onLine);

    return () => {
      window.removeEventListener('online', handleOnlineStatusChange);
      window.removeEventListener('offline', handleOnlineStatusChange);
    };
  }, []);

  // Filter classes for the selected day
  const filteredClasses = useMemo(() => {
    const dayClasses = classData[selectedDay] || [];

    // Sort classes by time
    return [...dayClasses].sort((a, b) => {
      // Extract start time for comparison (e.g., "08:30-10:00" -> "08:30")
      const aStart = a.timeValue.split("-")[0];
      const bStart = b.timeValue.split("-")[0];
      return aStart.localeCompare(bStart);
    });
  }, [classData, selectedDay]);

  // Load the last searched section from localStorage on initial load
  useEffect(() => {
    const loadInitialData = async () => {
      try {
        const lastSection = getLastSection();

        // Get next six days starting from today
        const nextSixDays = getNextSixDays();
        setDays(nextSixDays);

        const currentDay = getCurrentDay();
        setSelectedDay(currentDay.key);

        if (lastSection) {
          setSectionId(lastSection);
          await handleSearch(lastSection);
        }
      } catch (error) {
        console.error("Error loading initial data:", error);
      } finally {
        // Hide initial loading state after a short delay for smoother transition
        setTimeout(() => {
          setIsAppLoading(false);
        }, 500);
      }
    };

    loadInitialData();
  }, []);

  const handleSearch = async (value: string) => {
    if (!value.trim()) return;

    setIsSearching(true);
    setError(null);

    try {
      setIsLoading(true);
      let data;

      try {
        data = await fetchRoutineData();
      } catch (fetchError) {
        if (!navigator.onLine) {
          // Try to get data from cache if offline
          const cachedData = localStorage.getItem('routineData');
          if (cachedData) {
            data = JSON.parse(cachedData);
          } else {
            throw new Error('No cached data available while offline');
          }
        } else {
          throw fetchError;
        }
      }

      // Cache the data for offline use
      if (navigator.onLine && data) {
        localStorage.setItem('routineData', JSON.stringify(data));
      }

      // Find classes for the section
      const sectionClasses = findClassesForSection(data, value);

      if (Object.keys(sectionClasses).length === 0) {
        setError(`No classes found for section "${value}"`);
        setClassData({});
      } else {
        setClassData(sectionClasses);
        saveLastSection(value);
      }
    } catch (err) {
      console.error("Error searching for section:", err);
      setError(isOffline
        ? "You're offline. No cached data available for this section."
        : "Failed to load routine data. Please try again.");
      setClassData({});
    } finally {
      setIsLoading(false);
      setIsSearching(false);
    }
  };

  const handleDayChange = (day: string) => {
    if (day === selectedDay) return;

    setPrevSelectedDay(selectedDay);
    setIsTransitioning(true);
    setIsLoading(true);

    // Short delay to ensure CSS transitions have time to start
    setTimeout(() => {
      setSelectedDay(day);

      // Allow time for the new content to render before fading it in
      setTimeout(() => {
        setIsLoading(false);
        setIsTransitioning(false);
      }, 200);
    }, 50);
  };

  // Show app loading state
  if (isAppLoading) {
    return (
      <div className="fixed inset-0 flex flex-col items-center justify-center bg-background z-50">
        <div className="w-20 h-20 mb-6 relative">
          <div className="absolute inset-0 rounded-full bg-primary/20 animate-ping"></div>
          <div className="absolute inset-2 rounded-full bg-primary/40 animate-pulse"></div>
          <div className="absolute inset-4 rounded-full bg-primary/60 animate-pulse delay-75"></div>
          <div className="absolute inset-6 rounded-full bg-primary/80 animate-pulse delay-150"></div>
          <div className="absolute inset-8 rounded-full bg-primary animate-pulse delay-300"></div>
        </div>
        <h2 className="text-xl font-semibold animate-pulse">Class Routine Viewer</h2>
        <p className="text-muted-foreground text-sm mt-2">Loading your schedule...</p>
      </div>
    );
  }

  const structuredData = [
    generateWebsiteSchema(),
    generateEducationalAppSchema()
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-background to-background/95 transition-colors duration-300">
      {isOffline && (
        <div className="fixed top-0 left-0 right-0 bg-yellow-500 text-black z-50 py-1 px-4 text-center text-sm font-medium">
          <div className="flex items-center justify-center gap-2">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-4 h-4">
              <path strokeLinecap="round" strokeLinejoin="round" d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126z" />
            </svg>
            You&apos;re offline. Using cached data.
          </div>
        </div>
      )}
      <div className="max-w-7xl mx-auto px-3 sm:px-4 py-4 sm:py-6 lg:px-8">
        <header className="mb-6 sm:mb-8 text-center relative">
          <div className="absolute right-2 top-2 sm:right-0 sm:top-0 flex items-center gap-2">
            <button
              onClick={() => setShowMobileInfo(!showMobileInfo)}
              className="p-2 sm:p-3 rounded-full bg-card shadow-md hover:bg-accent border border-border/40 transition-all duration-300 md:hidden"
              aria-label="Show information"
            >
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-5 h-5 text-primary">
                <path strokeLinecap="round" strokeLinejoin="round" d="M11.25 11.25l.041-.02a.75.75 0 011.063.852l-.708 2.836a.75.75 0 001.063.853l.041-.021M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-9-3.75h.008v.008H12V8.25z" />
              </svg>
            </button>
            <button
              onClick={() => router.push('/setup')}
              className="p-2 sm:p-3 rounded-full bg-card shadow-md hover:bg-accent border border-border/40 transition-all duration-300 group"
              aria-label="Setup routine processor"
              title="Setup routine processor"
            >
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-5 h-5 text-primary group-hover:rotate-90 transition-transform duration-300">
                <path strokeLinecap="round" strokeLinejoin="round" d="M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.87.074.04.147.083.22.127.324.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 011.37.49l1.296 2.247a1.125 1.125 0 01-.26 1.431l-1.003.827c-.293.24-.438.613-.431.992a6.759 6.759 0 010 .255c-.007.378.138.75.43.99l1.005.828c.424.35.534.954.26 1.43l-1.298 2.247a1.125 1.125 0 01-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076.124a6.57 6.57 0 01-.22.128c-.331.183-.581.495-.644.869l-.213 1.28c-.09.543-.56.941-1.11.941h-2.594c-.55 0-1.02-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 01-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 01-1.369-.49l-1.297-2.247a1.125 1.125 0 01.26-1.431l1.004-.827c.292-.24.437-.613.43-.992a6.932 6.932 0 010-.255c.007-.378-.138-.75-.43-.99l-1.004-.828a1.125 1.125 0 01-.26-1.43l1.297-2.247a1.125 1.125 0 011.37-.491l1.216.456c.356.133.751.072 1.076-.124.072-.044.146-.087.22-.128.332-.183.582-.495.644-.869l.214-1.281z" />
                <path strokeLinecap="round" strokeLinejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
              </svg>
            </button>
            <button
              onClick={() => router.push('/data-converter')}
              className="p-2 sm:p-3 rounded-full bg-card shadow-md hover:bg-accent border border-border/40 transition-all duration-300 group"
              aria-label="Data converter"
              title="Convert Excel to JSON"
            >
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-5 h-5 text-primary group-hover:scale-110 transition-transform duration-300">
                <path strokeLinecap="round" strokeLinejoin="round" d="M7.5 21L3 16.5m0 0L7.5 12M3 16.5h13.5m0-13.5L21 7.5m0 0L16.5 12M21 7.5H7.5" />
              </svg>
            </button>
            <ThemeToggle />
          </div>

          <h1 className="text-2xl sm:text-4xl font-extrabold tracking-tight mb-2 sm:mb-4 animate-fade-in">
            <span className="bg-clip-text text-transparent bg-gradient-to-r from-primary to-purple-600">
              <span className="inline-block animate-bounce-subtle mr-2">📚</span>
              Class Routine Viewer
            </span>
          </h1>

          <div className={`max-w-2xl mx-auto mb-6 sm:mb-8 animate-fade-in text-sm sm:text-base transition-all duration-300 ${showMobileInfo ? 'md:block' : 'hidden md:block'}`}>
            <p className="text-muted-foreground">
              Find your class schedule by entering your section ID below.
              {showMobileInfo && (
                <span className="md:hidden block mt-2 px-4 py-2 bg-card rounded-lg border border-border/50 text-xs">
                  <strong className="text-primary">Pro tip:</strong> Your section ID typically follows the format &ldquo;XX_Y&rdquo; where XX is your batch number and Y is your section letter.
                </span>
              )}
            </p>
          </div>

          <div className="max-w-md mx-auto animate-slide-up">
            <SearchInput
              onSearch={handleSearch}
              initialValue={sectionId}
              placeholder="Enter section ID (e.g. 67_B)"
            />
          </div>
        </header>

        <main className="min-h-[50vh] relative z-10">
          {isSearching ? (
            <div className="flex flex-col items-center justify-center py-12 sm:py-16 transition-opacity duration-300">
              <LoadingSpinner size="lg" className="mb-4" />
              <p className="text-muted-foreground animate-pulse">
                Searching for classes...
              </p>
            </div>
          ) : error ? (
            <div className="transition-opacity duration-300 animate-fade-in">
              <EmptyState
                title="No Classes Found"
                message={error}
                icon={
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    strokeWidth={1}
                    stroke="currentColor"
                    className="w-12 h-12 text-muted-foreground"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z"
                    />
                  </svg>
                }
              />
            </div>
          ) : Object.keys(classData).length > 0 ? (
            <div className="animate-fade-in space-y-4">
              <div className="bg-card rounded-2xl shadow-lg p-2 sm:p-4 mb-4 sm:mb-6 animate-fade-in border border-border/50 hover:border-border/80 transition-colors">
                <div className="flex items-center justify-between mb-2 sm:mb-4 px-1 sm:px-2">
                  <h2 className="text-lg sm:text-xl font-semibold flex items-center">
                    <span className="inline-block mr-2 text-primary">
                      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-5 h-5">
                        <path d="M12.75 12.75a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0ZM7.5 15.75a.75.75 0 1 0 0-********* 0 0 0 0 1.5ZM8.25 17.25a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0ZM9.75 15.75a.75.75 0 1 0 0-********* 0 0 0 0 1.5ZM10.5 17.25a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0ZM12 15.75a.75.75 0 1 0 0-********* 0 0 0 0 1.5ZM12.75 17.25a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0ZM14.25 15.75a.75.75 0 1 0 0-********* 0 0 0 0 1.5ZM15 17.25a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0ZM16.5 15.75a.75.75 0 1 0 0-********* 0 0 0 0 1.5ZM15 12.75a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0ZM16.5 13.5a.75.75 0 1 0 0-********* 0 0 0 0 1.5Z" />
                        <path fillRule="evenodd" d="M6.75 2.25A.75.75 0 0 1 7.5 3v1.5h9V3A.75.75 0 0 1 18 3v1.5h.75a3 3 0 0 1 3 3v11.25a3 3 0 0 1-3 3H5.25a3 3 0 0 1-3-3V7.5a3 3 0 0 1 3-3H6V3a.75.75 0 0 1 .75-.75Zm13.5 9a1.5 1.5 0 0 0-1.5-1.5H5.25a1.5 1.5 0 0 0-1.5 1.5v7.5a1.5 1.5 0 0 0 1.5 1.5h13.5a1.5 1.5 0 0 0 1.5-1.5v-7.5Z" clipRule="evenodd" />
                      </svg>
                    </span>
                    Schedule for <span className="text-primary font-bold ml-1">{sectionId}</span>
                  </h2>

                  <div className="flex items-center">
                    <div className="h-2 w-2 rounded-full bg-green-500 mr-2"></div>
                    <span className="text-xs text-muted-foreground hidden sm:inline">Updated today</span>
                  </div>
                </div>
                <div className="flex justify-center">
                  <div className="overflow-x-auto hide-scrollbar max-w-full w-auto mx-auto">
                    <DaySelector
                      days={days}
                      selectedDay={selectedDay}
                      onSelectDay={handleDayChange}
                    />
                  </div>
                </div>
              </div>

              <div className="relative overflow-hidden rounded-xl border border-border/50 bg-card/50 backdrop-blur-sm shadow-md">
                {isLoading && (
                  <div className="absolute inset-0 flex items-center justify-center bg-background/90 z-20 transition-opacity duration-300 backdrop-blur-sm">
                    <LoadingSpinner size="md" />
                  </div>
                )}

                <div className="relative w-full">
                  {/* Current day content */}
                  <div
                    className={`transition-all duration-300 w-full ${
                      isTransitioning || isLoading ? 'opacity-0 transform translate-x-4' : 'opacity-100 transform translate-x-0'
                    } overflow-y-auto`}
                    style={{ minHeight: '450px', maxHeight: '70vh' }}
                  >
                    <ClassList
                      classData={filteredClasses}
                      selectedDay={selectedDay}
                    />
                  </div>

                  {/* Previous day content (keeps layout stable during transition) */}
                  {isTransitioning && (
                    <div
                      className="transition-opacity duration-300 absolute inset-0 opacity-0 transform -translate-x-4 overflow-y-auto"
                      style={{ minHeight: '450px', maxHeight: '70vh' }}
                    >
                      <ClassList
                        classData={classData[prevSelectedDay] || []}
                        selectedDay={prevSelectedDay}
                      />
                    </div>
                  )}
                </div>
              </div>

              <div className="mt-4 text-center">
                <p className="text-xs sm:text-sm text-muted-foreground flex items-center justify-center gap-1">
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" className="w-4 h-4">
                    <path fillRule="evenodd" d="M18 10a8 8 0 1 1-16 0 8 8 0 0 1 16 0Zm-7-4a1 1 0 1 1-2 0 1 1 0 0 1 2 0ZM9 9a.75.75 0 0 0 0 1.5h.253a.25.25 0 0 1 .244.304l-.459 2.066A1.75 1.75 0 0 0 10.747 15H11a.75.75 0 0 0 0-1.5h-.253a.25.25 0 0 1-.244-.304l.459-2.066A1.75 1.75 0 0 0 9.253 9H9Z" clipRule="evenodd" />
                  </svg>
                  Tap on a class card for more details
                </p>
              </div>
            </div>
          ) : (
            <div className="transition-opacity duration-300 animate-fade-in">
              <EmptyState
                title="Enter Your Section ID"
                message="Please enter your section ID in the search box above to see your class schedule."
                icon={
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    strokeWidth={1}
                    stroke="currentColor"
                    className="w-10 h-10 sm:w-12 sm:h-12 text-muted-foreground animate-pulse-slow"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      d="M9.879 7.519c1.171-1.025 3.071-1.025 4.242 0 1.172 1.025 1.172 2.687 0 3.712-.203.179-.43.326-.67.442-.745.361-1.45.999-1.45 1.827v.75M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-9 5.25h.008v.008H12v-.008z"
                    />
                  </svg>
                }
              />
            </div>
          )}
        </main>

        <footer className="mt-10 sm:mt-16 text-center text-xs sm:text-sm text-muted-foreground py-4 border-t border-border/30">
          <div className="flex flex-col sm:flex-row items-center justify-center gap-2 sm:gap-4">
            <p>© {new Date().getFullYear()} Class Routine Viewer</p>
            <div className="hidden sm:block">•</div>
            <p className="flex items-center">
              <span className="mr-1">Made with</span>
              <span className="text-red-500 animate-pulse">❤️</span>
              <span className="ml-1">by Daffodil International University</span>
            </p>
          </div>

          <div className="mt-3 flex items-center justify-center gap-2">
            <a href="#" className="text-primary hover:text-primary/80 transition-colors">Feedback</a>
            <span>•</span>
            <a href="#" className="text-primary hover:text-primary/80 transition-colors">Contact</a>
            <span>•</span>
            <a href="#" className="text-primary hover:text-primary/80 transition-colors">Help</a>
          </div>
        </footer>
      </div>

      {/* Background decorative elements */}
      <div className="fixed inset-0 -z-10 overflow-hidden pointer-events-none">
        <div className="absolute -top-[30%] -right-[10%] w-[70%] h-[70%] bg-gradient-to-b from-primary/5 to-transparent rounded-full blur-3xl opacity-50 animate-pulse-slow" />
        <div className="absolute -bottom-[30%] -left-[10%] w-[70%] h-[70%] bg-gradient-to-t from-purple-500/5 to-transparent rounded-full blur-3xl opacity-50 animate-pulse-slow" />
      </div>
      <JsonLd data={structuredData} />
    </div>
  );
}
