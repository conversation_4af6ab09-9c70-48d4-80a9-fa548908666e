"use client";

import { useRouter } from "next/navigation";
import ExcelToJsonConverter from "@/app/components/ExcelToJsonConverter";
import ThemeToggle from "@/app/components/ui/theme-toggle";

export default function DataConverterPage() {
  const router = useRouter();

  return (
    <div className="min-h-screen bg-gradient-to-br from-background to-background/95 transition-colors duration-300">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <header className="text-center mb-8">
          <div className="flex justify-between items-center mb-6">
            <button
              onClick={() => router.push("/")}
              className="flex items-center gap-2 text-muted-foreground hover:text-foreground transition-colors duration-200 group"
            >
              <svg 
                xmlns="http://www.w3.org/2000/svg" 
                fill="none" 
                viewBox="0 0 24 24" 
                strokeWidth={1.5} 
                stroke="currentColor" 
                className="w-5 h-5 group-hover:-translate-x-1 transition-transform duration-200"
              >
                <path strokeLinecap="round" strokeLinejoin="round" d="M10.5 19.5L3 12m0 0l7.5-7.5M3 12h18" />
              </svg>
              Back to Routine Viewer
            </button>
            <ThemeToggle />
          </div>

          <h1 className="text-3xl sm:text-5xl font-extrabold tracking-tight mb-4 animate-fade-in">
            <span className="bg-clip-text text-transparent bg-gradient-to-r from-primary to-purple-600">
              <span className="inline-block animate-bounce-subtle mr-2">🔄</span>
              Data Converter
            </span>
          </h1>
          
          <div className="max-w-3xl mx-auto mb-8 animate-fade-in">
            <p className="text-muted-foreground text-lg mb-4">
              Convert your Excel routine file into compressed JSON format and save it to the public folder for use with the routine viewer.
            </p>
            <div className="bg-card/50 border border-border/50 rounded-lg p-4 text-sm text-muted-foreground">
              <p className="mb-2">
                <strong className="text-primary">What this does:</strong>
              </p>
              <ol className="list-decimal list-inside space-y-1 text-left">
                <li>Parses your Excel routine file into a structured format</li>
                <li>Compresses the data using optimized keys (similar to Python script logic)</li>
                <li>Saves the compressed JSON files to the public/data folder</li>
                <li>Makes the data available for the routine viewer application</li>
              </ol>
              <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded">
                <p className="text-blue-800 dark:text-blue-200 text-xs">
                  <strong>💡 Note:</strong> This process replicates the Python script functionality but runs entirely in the browser and Node.js environment.
                </p>
              </div>
            </div>
          </div>
        </header>

        {/* Main Content */}
        <div className="max-w-4xl mx-auto">
          <ExcelToJsonConverter />
        </div>

        {/* Footer Info */}
        <footer className="mt-16 text-center">
          <div className="max-w-2xl mx-auto">
            <div className="bg-card/30 border border-border/30 rounded-lg p-6">
              <h3 className="font-semibold mb-3 flex items-center justify-center gap-2">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-5 h-5 text-primary">
                  <path strokeLinecap="round" strokeLinejoin="round" d="M11.25 11.25l.041-.02a.75.75 0 011.063.852l-.708 2.836a.75.75 0 001.063.853l.041-.021M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-9-3.75h.008v.008H12V8.25z" />
                </svg>
                How the Compression Works
              </h3>
              <div className="text-sm text-muted-foreground space-y-2 text-left">
                <p><strong>Day Keys:</strong> First 3 letters (mon, tue, wed, etc.)</p>
                <p><strong>Time Slots:</strong> t1, t2, t3... (t1 = first time slot, t2 = second, etc.)</p>
                <p><strong>Room IDs:</strong> Lowercase with hyphens removed (kt201 for KT-201)</p>
                <p><strong>Class Data:</strong> c = course, s = section, t = teacher</p>
              </div>
            </div>
          </div>
        </footer>
      </div>
    </div>
  );
}
