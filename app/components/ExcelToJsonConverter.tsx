"use client";

import { useState, useRef } from "react";
import { cn } from "@/app/lib/utils";

interface ProcessingStep {
  id: string;
  name: string;
  status: 'pending' | 'processing' | 'completed' | 'error';
  message?: string;
}

interface ProcessingResult {
  totalDays: number;
  savedFiles: string[];
  keyGuide: any;
  preview: {
    days: string[];
    sampleData: any;
  };
}

export default function ExcelToJsonConverter() {
  const [file, setFile] = useState<File | null>(null);
  const [filename, setFilename] = useState<string>('compressed_routine.json');
  const [isProcessing, setIsProcessing] = useState(false);
  const [result, setResult] = useState<ProcessingResult | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [steps, setSteps] = useState<ProcessingStep[]>([
    { id: 'parse', name: 'Parse Excel File', status: 'pending' },
    { id: 'compress', name: 'Compress Data Structure', status: 'pending' },
    { id: 'save', name: 'Save to Public Folder', status: 'pending' }
  ]);

  const fileInputRef = useRef<HTMLInputElement>(null);

  const updateStep = (stepId: string, status: ProcessingStep['status'], message?: string) => {
    setSteps(prev => prev.map(step =>
      step.id === stepId ? { ...step, status, message } : step
    ));
  };

  const resetSteps = () => {
    setSteps(prev => prev.map(step => ({ ...step, status: 'pending', message: undefined })));
  };

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = event.target.files?.[0];
    if (selectedFile) {
      if (selectedFile.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
          selectedFile.name.endsWith('.xlsx')) {
        setFile(selectedFile);
        setError(null);
        setResult(null);
        resetSteps();
      } else {
        setError('Please select a valid Excel file (.xlsx format)');
        setFile(null);
      }
    }
  };

  const handleDrop = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    const droppedFile = event.dataTransfer.files[0];
    if (droppedFile) {
      if (droppedFile.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
          droppedFile.name.endsWith('.xlsx')) {
        setFile(droppedFile);
        setError(null);
        setResult(null);
        resetSteps();
      } else {
        setError('Please select a valid Excel file (.xlsx format)');
      }
    }
  };

  const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
  };

  const processFile = async () => {
    if (!file) return;

    setIsProcessing(true);
    setError(null);
    setResult(null);

    try {
      // Step 1: Parse Excel File
      updateStep('parse', 'processing', 'Reading and parsing Excel file...');

      const formData = new FormData();
      formData.append('file', file);
      formData.append('filename', filename);

      const response = await fetch('/api/excel-to-json', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to process file');
      }

      updateStep('parse', 'completed', 'Excel file parsed successfully');
      updateStep('compress', 'processing', 'Compressing data structure...');

      // Simulate processing time for better UX
      await new Promise(resolve => setTimeout(resolve, 1000));
      updateStep('compress', 'completed', 'Data structure compressed');
      updateStep('save', 'processing', 'Saving to public folder...');

      await new Promise(resolve => setTimeout(resolve, 500));
      updateStep('save', 'completed', 'Files saved successfully');

      // Get the result
      const responseData = await response.json();
      setResult(responseData.data);

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An unexpected error occurred';
      setError(errorMessage);

      // Mark current processing step as error
      const currentStep = steps.find(step => step.status === 'processing');
      if (currentStep) {
        updateStep(currentStep.id, 'error', errorMessage);
      }
    } finally {
      setIsProcessing(false);
    }
  };

  const clearFile = () => {
    setFile(null);
    setError(null);
    setResult(null);
    resetSteps();
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  return (
    <div className="space-y-8">
      {/* File Upload Section */}
      <div className="bg-card rounded-xl p-6 border border-border/50 shadow-md">
        <h2 className="text-xl font-semibold mb-4 flex items-center gap-2">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-5 h-5 text-primary">
            <path strokeLinecap="round" strokeLinejoin="round" d="M3 16.5v2.25A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75V16.5m-13.5-9L12 3m0 0l4.5 4.5M12 3v13.5" />
          </svg>
          Upload Excel File
        </h2>

        {/* Filename Input */}
        <div className="mb-4">
          <label htmlFor="filename" className="block text-sm font-medium text-foreground mb-2">
            Output Filename
          </label>
          <input
            id="filename"
            type="text"
            value={filename}
            onChange={(e) => setFilename(e.target.value)}
            className="w-full px-3 py-2 border border-border rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
            placeholder="compressed_routine.json"
          />
          <p className="text-xs text-muted-foreground mt-1">
            The JSON file will be saved in the public/data folder
          </p>
        </div>

        <div
          onDrop={handleDrop}
          onDragOver={handleDragOver}
          className={cn(
            "border-2 border-dashed rounded-lg p-8 text-center transition-all duration-200",
            file ? "border-primary/50 bg-primary/5" : "border-border hover:border-primary/50 hover:bg-primary/5",
            "cursor-pointer"
          )}
          onClick={() => fileInputRef.current?.click()}
        >
          <input
            ref={fileInputRef}
            type="file"
            accept=".xlsx"
            onChange={handleFileSelect}
            className="hidden"
          />

          {file ? (
            <div className="space-y-2">
              <div className="w-12 h-12 mx-auto bg-primary/10 rounded-lg flex items-center justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-6 h-6 text-primary">
                  <path strokeLinecap="round" strokeLinejoin="round" d="M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 00-9-9z" />
                </svg>
              </div>
              <p className="font-medium text-foreground">{file.name}</p>
              <p className="text-sm text-muted-foreground">
                {(file.size / 1024 / 1024).toFixed(2)} MB
              </p>
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  clearFile();
                }}
                className="text-sm text-destructive hover:text-destructive/80 transition-colors"
              >
                Remove file
              </button>
            </div>
          ) : (
            <div className="space-y-2">
              <div className="w-12 h-12 mx-auto bg-muted rounded-lg flex items-center justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-6 h-6 text-muted-foreground">
                  <path strokeLinecap="round" strokeLinejoin="round" d="M3 16.5v2.25A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75V16.5m-13.5-9L12 3m0 0l4.5 4.5M12 3v13.5" />
                </svg>
              </div>
              <p className="text-foreground font-medium">
                Drop your Excel file here or click to browse
              </p>
              <p className="text-sm text-muted-foreground">
                Supports .xlsx files up to 10MB
              </p>
            </div>
          )}
        </div>

        {error && (
          <div className="mt-4 p-3 bg-destructive/10 border border-destructive/20 rounded-lg">
            <p className="text-sm text-destructive">{error}</p>
          </div>
        )}
      </div>

      {/* Process Button */}
      {file && (
        <div className="flex justify-center">
          <button
            onClick={processFile}
            disabled={isProcessing}
            className={cn(
              "px-8 py-3 rounded-lg font-medium transition-all duration-200",
              "bg-primary text-primary-foreground hover:bg-primary/90",
              "disabled:opacity-50 disabled:cursor-not-allowed",
              "flex items-center gap-2"
            )}
          >
            {isProcessing ? (
              <>
                <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
                Processing...
              </>
            ) : (
              <>
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-4 h-4">
                  <path strokeLinecap="round" strokeLinejoin="round" d="M5.25 5.653c0-.856.917-1.398 1.667-.986l11.54 6.348a1.125 1.125 0 010 1.971l-11.54 6.347a1.125 1.125 0 01-1.667-.985V5.653z" />
                </svg>
                Convert to JSON
              </>
            )}
          </button>
        </div>
      )}

      {/* Processing Steps */}
      {(isProcessing || steps.some(step => step.status !== 'pending')) && (
        <div className="bg-card rounded-xl p-6 border border-border/50 shadow-md">
          <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-5 h-5 text-primary">
              <path strokeLinecap="round" strokeLinejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            Processing Steps
          </h3>

          <div className="space-y-4">
            {steps.map((step, index) => (
              <div key={step.id} className="flex items-center gap-4">
                <div className={cn(
                  "w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium transition-all duration-200",
                  step.status === 'pending' && "bg-muted text-muted-foreground",
                  step.status === 'processing' && "bg-primary text-primary-foreground animate-pulse",
                  step.status === 'completed' && "bg-green-500 text-white",
                  step.status === 'error' && "bg-destructive text-destructive-foreground"
                )}>
                  {step.status === 'pending' && (index + 1)}
                  {step.status === 'processing' && (
                    <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
                  )}
                  {step.status === 'completed' && (
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={2} stroke="currentColor" className="w-4 h-4">
                      <path strokeLinecap="round" strokeLinejoin="round" d="M4.5 12.75l6 6 9-13.5" />
                    </svg>
                  )}
                  {step.status === 'error' && (
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={2} stroke="currentColor" className="w-4 h-4">
                      <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  )}
                </div>

                <div className="flex-1">
                  <p className={cn(
                    "font-medium",
                    step.status === 'pending' && "text-muted-foreground",
                    step.status === 'processing' && "text-primary",
                    step.status === 'completed' && "text-green-600 dark:text-green-400",
                    step.status === 'error' && "text-destructive"
                  )}>
                    {step.name}
                  </p>
                  {step.message && (
                    <p className="text-sm text-muted-foreground mt-1">{step.message}</p>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Results Section */}
      {result && (
        <div className="bg-card rounded-xl p-6 border border-border/50 shadow-md">
          <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-5 h-5 text-green-500">
              <path strokeLinecap="round" strokeLinejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            Conversion Complete
          </h3>

          <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4 mb-6">
            <p className="text-green-800 dark:text-green-200 mb-2">
              ✅ Your Excel file has been successfully converted to compressed JSON format!
            </p>
            <div className="text-sm text-green-700 dark:text-green-300">
              <p>📊 <strong>Days processed:</strong> {result.totalDays}</p>
              <p>📁 <strong>Files saved:</strong> {result.savedFiles.length}</p>
            </div>
          </div>

          {/* Saved Files */}
          <div className="mb-6">
            <h4 className="font-medium mb-3">📁 Saved Files</h4>
            <div className="space-y-2">
              {result.savedFiles.map((file, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-muted rounded-lg">
                  <div className="flex items-center gap-2">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-4 h-4 text-primary">
                      <path strokeLinecap="round" strokeLinejoin="round" d="M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m6.75 18H5.625c-.621 0-1.125-.504-1.125-1.125V5.625c0-.621.504-1.125 1.125-1.125h9.75c.621 0 1.125.504 1.125 1.125v1.5h2.25a1.125 1.125 0 011.125 1.125V19.5a1.125 1.125 0 01-1.125 1.125H8.25z" />
                    </svg>
                    <span className="text-sm font-medium">public/{file}</span>
                  </div>
                  <a
                    href={`/${file}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-xs text-primary hover:text-primary/80 transition-colors"
                  >
                    View
                  </a>
                </div>
              ))}
            </div>
          </div>

          {/* Preview */}
          {result.preview.days.length > 0 && (
            <div>
              <h4 className="font-medium mb-3">👀 Data Preview</h4>
              <div className="bg-muted rounded-lg p-4">
                <p className="text-sm text-muted-foreground mb-2">
                  <strong>Days found:</strong> {result.preview.days.join(', ')}
                </p>
                <details className="text-sm">
                  <summary className="cursor-pointer text-primary hover:text-primary/80 transition-colors">
                    View sample data structure
                  </summary>
                  <pre className="mt-2 p-3 bg-background rounded border text-xs overflow-x-auto">
                    {JSON.stringify(result.preview.sampleData, null, 2)}
                  </pre>
                </details>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
