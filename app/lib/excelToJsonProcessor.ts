import * as XLSX from 'xlsx';
import { writeFile } from 'fs/promises';
import { join } from 'path';

// Types for the compressed routine data structure
interface CompressedClassData {
  c: string; // course
  s: string; // section
  t: string; // teacher
}

interface CompressedTimeSlot {
  v: string; // time value
  c: { [roomId: string]: CompressedClassData }; // classes
}

interface CompressedDayData {
  [timeSlotKey: string]: CompressedTimeSlot;
}

interface CompressedRoutineData {
  [dayKey: string]: CompressedDayData;
}

// Function to parse Excel file to JSON (similar to PDF parsing logic)
export function parseExcelToJsonArray(buffer: Buffer): any[] {
  const workbook = XLSX.read(buffer, { type: 'buffer' });
  
  // Get the first sheet (assuming routine data is in the first sheet)
  const firstSheetName = workbook.SheetNames[0];
  const worksheet = workbook.Sheets[firstSheetName];
  
  // Convert to JSON array with column indices as keys
  const jsonData = XLSX.utils.sheet_to_json(worksheet, { 
    header: 1, // Use array of arrays format
    defval: "" // Default value for empty cells
  });
  
  // Convert to the format expected by the compression logic
  const formattedData: any[] = [];
  
  jsonData.forEach((row: any[]) => {
    const rowObject: { [key: string]: string } = {};
    row.forEach((cell: any, index: number) => {
      rowObject[index.toString()] = cell ? cell.toString().trim() : "";
    });
    formattedData.push(rowObject);
  });
  
  return formattedData;
}

// Function to compress routine data (equivalent to structure.py logic)
export function compressRoutineData(data: any[]): CompressedRoutineData {
  const result: CompressedRoutineData = {};
  let currentDay: string | null = null;
  let timeSlots: string[] = [];
  
  // Days of the week for case-insensitive comparison
  const daysOfWeek = ["monday", "tuesday", "wednesday", "thursday", "friday", "saturday", "sunday"];
  
  // Process the data
  for (const item of data) {
    // Check if this item contains a day name
    if (item["0"] && item["0"].trim()) {
      const itemText = item["0"].toLowerCase();
      const foundDay = daysOfWeek.find(day => itemText.includes(day));
      
      if (foundDay) {
        currentDay = foundDay.substring(0, 3); // Store first 3 chars of day name
        result[currentDay] = {};
        timeSlots = []; // Reset time slots for new day
        continue;
      }
    }
    
    // Check if this is the time slots row (comes right after day name)
    if (currentDay && timeSlots.length === 0 && item["0"] && item["0"].includes(":")) {
      // Extract time slots (positions 0, 3, 6, 9, 12, 15)
      timeSlots = [
        item["0"]?.trim() || "",
        item["3"]?.trim() || "",
        item["6"]?.trim() || "",
        item["9"]?.trim() || "",
        item["12"]?.trim() || "",
        item["15"]?.trim() || ""
      ].filter(slot => slot && slot.includes(":"));
      
      // Initialize dictionary for each time slot
      timeSlots.forEach((slot, i) => {
        if (slot) {
          const timeSlotKey = `t${i + 1}`;
          result[currentDay!][timeSlotKey] = {
            v: slot,
            c: {}
          };
        }
      });
      continue;
    }
    
    // Process class data (comes after time slots row and column headers row)
    if (currentDay && timeSlots.length > 0 && item["0"] && item["0"] !== "Room") {
      // Process each time slot for this room
      [0, 3, 6, 9, 12, 15].forEach((slotPosition, slotIndex) => {
        if (slotIndex < timeSlots.length && timeSlots[slotIndex]) {
          const room = item[slotPosition.toString()]?.trim() || "";
          const courseSectionRaw = item[(slotPosition + 1).toString()]?.trim() || "";
          const teacher = item[(slotPosition + 2).toString()]?.trim() || "";
          
          // Only process if we have a room
          if (room) {
            // Parse course and section using regex
            const courseSectionMatch = courseSectionRaw.match(/([A-Z]+\d+)\((.+?)\)/);
            
            if (courseSectionMatch) {
              const course = courseSectionMatch[1];
              const section = courseSectionMatch[2];
              
              // Add to result with compressed keys
              const timeSlotKey = `t${slotIndex + 1}`;
              const roomId = room.replace("-", "").toLowerCase(); // E.g., 'kt201' instead of 'KT-201'
              
              if (result[currentDay][timeSlotKey]) {
                result[currentDay][timeSlotKey].c[roomId] = {
                  c: course,
                  s: section,
                  t: teacher
                };
              }
            }
          }
        }
      });
    }
  }
  
  return result;
}

// Function to generate key guide (equivalent to structure.py key guide)
export function generateKeyGuide() {
  return {
    "Day keys": "First 3 letters of day name (mon, tue, wed, thu, fri, sat, sun)",
    "Time slot keys": {
      "t1, t2, t3...": "Time slots in order (t1 is first time slot, t2 is second, etc.)",
      "v": "The actual time value (e.g., '08:30-10:00')",
      "c": "Classes in this time slot"
    },
    "Room keys": "Room ID with hyphens removed and lowercase (e.g., kt201 for KT-201). To get the original room name, convert to uppercase and add a hyphen after the first 2 characters.",
    "Class data": {
      "c": "Course code (e.g., 'CSE101')",
      "s": "Section (e.g., '67_B')",
      "t": "Teacher initial/name (e.g., 'JDH')"
    }
  };
}

// Function to save compressed data to public folder
export async function saveToPublicFolder(
  compressedData: CompressedRoutineData,
  filename: string = 'compressed_routine.json'
): Promise<void> {
  const publicPath = join(process.cwd(), 'public', 'data');
  const filePath = join(publicPath, filename);
  
  // Save compressed data
  await writeFile(filePath, JSON.stringify(compressedData), 'utf-8');
  
  // Save readable version
  const readableFilePath = join(publicPath, filename.replace('.json', '_readable.json'));
  await writeFile(readableFilePath, JSON.stringify(compressedData, null, 2), 'utf-8');
  
  // Save key guide
  const keyGuide = generateKeyGuide();
  const keyGuideFilePath = join(publicPath, 'key_guide.json');
  await writeFile(keyGuideFilePath, JSON.stringify(keyGuide, null, 2), 'utf-8');
}

// Main processing function that combines all steps
export async function processExcelToCompressedJson(
  buffer: Buffer,
  outputFilename: string = 'compressed_routine.json'
): Promise<{
  compressedData: CompressedRoutineData;
  keyGuide: any;
  savedFiles: string[];
}> {
  // Step 1: Parse Excel to JSON array
  const jsonArray = parseExcelToJsonArray(buffer);
  
  // Step 2: Compress the data
  const compressedData = compressRoutineData(jsonArray);
  
  // Step 3: Generate key guide
  const keyGuide = generateKeyGuide();
  
  // Step 4: Save to public folder
  await saveToPublicFolder(compressedData, outputFilename);
  
  const savedFiles = [
    `data/${outputFilename}`,
    `data/${outputFilename.replace('.json', '_readable.json')}`,
    'data/key_guide.json'
  ];
  
  return {
    compressedData,
    keyGuide,
    savedFiles
  };
}
