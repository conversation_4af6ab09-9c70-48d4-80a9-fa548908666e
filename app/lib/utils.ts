import { ClassDetails, GroupedClasses, RoutineData } from "./types";

export function cn(...classes: (string | boolean | undefined)[]) {
  return classes.filter(Boolean).join(" ");
}

const CACHE_KEY = "routine_data";
const CACHE_DURATION = 7 * 24 * 60 * 60 * 1000; // 7 days in milliseconds
const LAST_SECTION_CACHE_KEY = "last_section";

export async function fetchRoutineData(): Promise<RoutineData> {
  // Check if data is in cache and not expired
  const cached = getFromCache<RoutineData>(CACHE_KEY);
  if (cached) {
    console.log("Using cached routine data");
    return cached;
  }

  try {
    const response = await fetch("/data/compressed_routine.json", {
      cache: 'no-cache', // Ensure we get fresh data when online
      headers: {
        'Cache-Control': 'no-cache'
      }
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch routine data: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();

    // Validate the data structure
    if (!data || typeof data !== 'object') {
      throw new Error("Invalid routine data format");
    }

    // Cache the data
    setInCache(CACHE_KEY, data, CACHE_DURATION);
    console.log("Fetched and cached new routine data");

    return data;
  } catch (error) {
    console.error("Error fetching routine data:", error);

    // If we're offline, try to get any available cached data (even if expired)
    if (!navigator.onLine) {
      const expiredCache = getExpiredCache<RoutineData>(CACHE_KEY);
      if (expiredCache) {
        console.log("Using expired cache data while offline");
        return expiredCache;
      }
    }

    throw error;
  }
}

export function getFromCache<T>(key: string): T | null {
  if (typeof window === "undefined") return null;

  try {
    const cached = localStorage.getItem(key);
    if (!cached) return null;

    const { data, expiry } = JSON.parse(cached);
    if (expiry && Date.now() > expiry) {
      localStorage.removeItem(key);
      return null;
    }

    return data as T;
  } catch (error) {
    console.error("Error getting from cache:", error);
    return null;
  }
}

export function setInCache(key: string, data: unknown, duration: number = CACHE_DURATION): void {
  if (typeof window === "undefined") return;

  try {
    const item = {
      data,
      expiry: Date.now() + duration
    };
    localStorage.setItem(key, JSON.stringify(item));
  } catch (error) {
    console.error("Error setting in cache:", error);
  }
}

export function saveLastSection(section: string): void {
  if (typeof window === "undefined") return;
  localStorage.setItem(LAST_SECTION_CACHE_KEY, section);
}

export function getLastSection(): string | null {
  if (typeof window === "undefined") return null;
  return localStorage.getItem(LAST_SECTION_CACHE_KEY);
}

// Get cached data even if expired (useful for offline scenarios)
export function getExpiredCache<T>(key: string): T | null {
  if (typeof window === "undefined") return null;

  try {
    const cached = localStorage.getItem(key);
    if (!cached) return null;

    const { data } = JSON.parse(cached);
    return data as T;
  } catch (error) {
    console.error("Error getting expired cache:", error);
    return null;
  }
}

// Check if we have any cached data (expired or not)
export function hasCachedData(key: string): boolean {
  if (typeof window === "undefined") return false;
  return localStorage.getItem(key) !== null;
}

// Get cache info (when it was cached, if expired, etc.)
export function getCacheInfo(key: string): {
  exists: boolean;
  expired: boolean;
  cachedAt?: Date;
  expiresAt?: Date;
} {
  if (typeof window === "undefined") {
    return { exists: false, expired: true };
  }

  try {
    const cached = localStorage.getItem(key);
    if (!cached) {
      return { exists: false, expired: true };
    }

    const { expiry } = JSON.parse(cached);
    const now = Date.now();
    const isExpired = expiry && now > expiry;

    return {
      exists: true,
      expired: isExpired,
      cachedAt: expiry ? new Date(expiry - CACHE_DURATION) : undefined,
      expiresAt: expiry ? new Date(expiry) : undefined
    };
  } catch (error) {
    console.error("Error getting cache info:", error);
    return { exists: false, expired: true };
  }
}

// Clear all app caches
export function clearAllCaches(): void {
  if (typeof window === "undefined") return;

  const keys = Object.keys(localStorage);
  keys.forEach(key => {
    if (key.startsWith('routine_') || key.startsWith('sectionData_') || key === LAST_SECTION_CACHE_KEY) {
      localStorage.removeItem(key);
    }
  });

  console.log("All app caches cleared");
}
export function findClassesForSection(data: RoutineData, sectionId: string): GroupedClasses {
  const result: GroupedClasses = {};

  Object.entries(data).forEach(([dayKey, daySchedule]) => {
    const dayClasses: ClassDetails[] = [];

    console.log(dayKey);

    Object.entries(daySchedule).forEach(([timeSlotKey, timeSlot]) => {
      const timeValue = timeSlot.v;
      const classes = timeSlot.c;

      Object.entries(classes).forEach(([roomKey, classInfo]) => {
        if (
          classInfo.s === sectionId ||
          classInfo.s === sectionId + "1" ||
          classInfo.s === sectionId + "2"
        ) {
          dayClasses.push({
            day: dayKey,
            timeSlot: timeSlotKey,
            timeValue,
            room: roomKey,
            course: classInfo.c,
            section: classInfo.s,
            teacher: classInfo.t,
          });
        }
      });
    });

    console.log(dayClasses);

    if (dayClasses.length > 0) {
      const merged: Record<string, ClassDetails & { timeValues: string[] }> = {};

      dayClasses.forEach((cls) => {
        const key = `${cls.room}-${cls.course}-${cls.section}-${cls.teacher}`;
        if (!merged[key]) {
          merged[key] = {
            ...cls,
            timeValues: [cls.timeValue],
          };
        } else {
          merged[key].timeValues.push(cls.timeValue);
        }
      });

      const mergedClasses: ClassDetails[] = Object.values(merged).map((mergedCls) => {
        const starts = mergedCls.timeValues.map(v => v.split('-')[0]);
        const ends = mergedCls.timeValues.map(v => v.split('-')[1]);

        const earliestStart = starts[0]; // Assume already sorted
        const latestEnd = ends[ends.length - 1]; // Assume already sorted

        return {
          ...mergedCls,
          timeValue: `${earliestStart}-${latestEnd}`,
        };
      });

      // Sort by start time
      mergedClasses.sort((a, b) => {
        const aHour = getHour(a.timeValue.split('-')[0]);
        const bHour = getHour(b.timeValue.split('-')[0]);
        return aHour - bHour;
      });

      result[dayKey] = mergedClasses;
    }
  });

  console.log(result);

  return result;
}

function getHour(time: string): number {
  const [hourStr] = time.split(':');
  let hour = parseInt(hourStr, 10);

  // Simple assumption: 1 to 6 => PM, so add 12
  if (hour >= 1 && hour <= 6) {
    hour += 12;
  }

  return hour;
}

export function formatRoomId(roomId: string): string {
  // Convert room IDs like "kt201" to "KT-201"
  if (roomId.length >= 5) {
    const prefix = roomId.slice(0, 2).toUpperCase();
    const number = roomId.slice(2);
    return `${prefix}-${number}`;
  }
  return roomId.toUpperCase();
}

export function getDayName(dayKey: string): string {
  const days: Record<string, string> = {
    "sat": "Saturday",
    "sun": "Sunday",
    "mon": "Monday",
    "tue": "Tuesday",
    "wed": "Wednesday",
    "thu": "Thursday",
    "fri": "Friday"
  };

  return days[dayKey.toLowerCase()] || dayKey;
}

export function getCurrentWeekDays() {
  const today = new Date();
  const days = [];

  // Create array of day keys
  const dayKeys = ["sat", "sun", "mon", "tue", "wed", "thu"];

  // Find Saturday (start of the week)
  const todayDay = today.getDay(); // 0 = Sunday, 1 = Monday, etc.
  const daysToSaturday = todayDay === 0 ? 6 : todayDay - 1;
  const saturday = new Date(today);
  saturday.setDate(today.getDate() - daysToSaturday);

  // Create 6 days (Saturday to Thursday)
  for (let i = 0; i < 6; i++) {
    const date = new Date(saturday);
    date.setDate(saturday.getDate() + i);

    days.push({
      key: dayKeys[i],
      name: getDayName(dayKeys[i]),
      date
    });
  }

  return days;
}

export function getNextSixDays() {
  const days = [];
  const today = new Date();

  // Map JavaScript day index to our day keys with proper typing
  const dayKeyMap: Record<number, string> = {
    0: "sun", // Sunday
    1: "mon", // Monday
    2: "tue", // Tuesday
    3: "wed", // Wednesday
    4: "thu", // Thursday
    5: "fri", // Friday
    6: "sat"  // Saturday
  };

  // Create array for the next 6 days starting from today
  for (let i = 0; i < 6; i++) {
    const date = new Date(today);
    date.setDate(today.getDate() + i);

    // Get the day of the week (0-6)
    const dayIndex = date.getDay();

    // Get the corresponding day key
    const dayKey = dayKeyMap[dayIndex];

    // Skip Friday (we don't have classes on Friday)
    if (dayIndex === 5) { // Friday
      continue;
    }

    days.push({
      key: dayKey,
      name: getDayName(dayKey),
      date: date
    });
  }

  // If we skipped any days and don't have 6 days yet, add more days
  if (days.length < 6) {
    const lastDate = new Date(days[days.length - 1].date);

    while (days.length < 6) {
      lastDate.setDate(lastDate.getDate() + 1);
      const dayIndex = lastDate.getDay();

      // Skip Friday
      if (dayIndex === 5) {
        continue;
      }

      const dayKey = dayKeyMap[dayIndex];

      days.push({
        key: dayKey,
        name: getDayName(dayKey),
        date: new Date(lastDate)
      });
    }
  }

  return days;
}

export function getCurrentDay() {
  const today = new Date();
  const dayIndex = today.getDay(); // 0 = Sunday, 1 = Monday, etc.

  // Map to our day keys with proper typing
  const dayKeyMap: Record<number, string> = {
    0: "sun", // Sunday
    1: "mon", // Monday
    2: "tue", // Tuesday
    3: "wed", // Wednesday
    4: "thu", // Thursday
    5: "fri", // Friday (not used)
    6: "sat"  // Saturday
  };

  // Get the current day key
  const currentDayKey = dayKeyMap[dayIndex];

  // If it's Friday, use the next available day (Saturday)
  if (dayIndex === 5) {
    const tomorrow = new Date(today);
    tomorrow.setDate(today.getDate() + 1);
    return { key: "sat", name: getDayName("sat"), date: tomorrow };
  }

  // Return the current day
  return {
    key: currentDayKey,
    name: getDayName(currentDayKey),
    date: today
  };
}