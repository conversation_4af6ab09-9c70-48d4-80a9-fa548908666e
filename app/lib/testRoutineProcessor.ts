// Test function to verify our routine processor works correctly
import { createExcelFromData } from './routineProcessor';

// Sample test data in the expected format
const testData = {
  "SATURDAY": {
    "08:30-10:00": {
      "KT-201": {
        "course": "CSE423",
        "section": "62_G",
        "teacher": "DAA"
      },
      "KT-208": {
        "course": "ENG102",
        "section": "67_B",
        "teacher": "SMH"
      }
    },
    "10:00-11:30": {
      "KT-201": {
        "course": "PHY102",
        "section": "67_B",
        "teacher": "MAK"
      }
    }
  },
  "SUNDAY": {
    "08:30-10:00": {
      "KT-201": {
        "course": "MAT101",
        "section": "68_A",
        "teacher": "MRR"
      }
    }
  }
};

export async function testExcelGeneration() {
  try {
    const buffer = await createExcelFromData(testData);
    console.log('Excel generation test passed! Buffer size:', buffer.length);
    return true;
  } catch (error) {
    console.error('Excel generation test failed:', error);
    return false;
  }
}

// Export test data for use in other tests
export { testData };
