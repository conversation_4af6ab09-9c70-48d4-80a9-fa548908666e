/* eslint-disable @typescript-eslint/no-explicit-any */
import * as XLSX from 'xlsx';
import * as ExcelJS from 'exceljs';

// Types for the routine data structure
interface ClassData {
  course: string;
  section: string;
  teacher: string;
}

interface TimeSlotData {
  [roomName: string]: ClassData;
}

interface DayData {
  [timeSlot: string]: TimeSlotData;
}

interface OrganizedRoutineData {
  [day: string]: DayData;
}

// Function to parse Excel file to JSON (equivalent to xlparser.js)
export function parseExcelToJson(buffer: Buffer): any {
  const workbook = XLSX.read(buffer, { type: 'buffer' });
  
  const allData: any = {};
  workbook.SheetNames.forEach(sheetName => {
    const sheet = workbook.Sheets[sheetName];
    const data = XLSX.utils.sheet_to_json(sheet, { defval: "" });
    allData[sheetName] = data;
  });
  
  return allData;
}

// Function to identify day names in an object
function isDayObject(obj: any): boolean {
  const dayNames = ['SATURDAY', 'SUNDAY', 'MONDAY', 'TUESDAY', 'WEDNESDAY', 'THURSDAY', 'FRIDAY'];
  
  const firstKeyValue = obj["   "] || '';
  
  return dayNames.some(day => firstKeyValue.toUpperCase().includes(day));
}

// Function to get the cleaned day name from an object
function getDayName(obj: any): string | null {
  const dayNames = ['SATURDAY', 'SUNDAY', 'MONDAY', 'TUESDAY', 'WEDNESDAY', 'THURSDAY', 'FRIDAY'];
  const firstKeyValue = obj["   "] || '';
  
  for (const day of dayNames) {
    if (firstKeyValue.toUpperCase().includes(day)) {
      return day;
    }
  }
  return null;
}

// Function to transform routine data (equivalent to transform_routine.js)
export function transformRoutineData(data: any): OrganizedRoutineData {
  const organizedData: OrganizedRoutineData = {};
  let currentDay: string | null = null;
  let timeSlots: string[] = [];
  let isTimeSlotRow = false;
  let isTitleRow = false;

  // Get the main data array
  const classRoutineKey = Object.keys(data).find(key => key.includes("Class Routine"));
  const items = classRoutineKey ? data[classRoutineKey] : data[Object.keys(data)[0]];
  
  if (!items || !Array.isArray(items)) {
    throw new Error('Invalid data format: Expected array of routine items');
  }

  for (let i = 0; i < items.length; i++) {
    const item = items[i];
    
    // Check if this is a day object
    if (isDayObject(item)) {
      currentDay = getDayName(item);
      if (currentDay) {
        organizedData[currentDay] = {};
        isTimeSlotRow = true;
      }
      continue;
    }
    
    // Check if this is a time slot row
    if (isTimeSlotRow && currentDay) {
      timeSlots = [
        item["   "],
        item["__EMPTY_2"],
        item["__EMPTY_5"],
        item["__EMPTY_8"],
        item["__EMPTY_11"],
        item["__EMPTY_14"]
      ].filter(slot => slot && slot.includes('-'));
      
      for (const timeSlot of timeSlots) {
        organizedData[currentDay][timeSlot] = {};
      }
      
      isTimeSlotRow = false;
      isTitleRow = true;
      continue;
    }
    
    // Skip title row
    if (isTitleRow) {
      isTitleRow = false;
      continue;
    }
    
    // Process class data rows
    if (currentDay && timeSlots.length > 0 && item["   "]) {
      
      for (let slotIndex = 0; slotIndex < timeSlots.length; slotIndex++) {
        const timeSlot = timeSlots[slotIndex];
  
        
        const roomName = slotIndex == 0 ? item["   "] : item[`__EMPTY_${(slotIndex * 3)-1}`];
        const course = slotIndex == 0 ? item["__EMPTY"] : item[`__EMPTY_${(slotIndex * 3)}`];
        const teacher = slotIndex == 0 ? item["__EMPTY_1"] : item[`__EMPTY_${(slotIndex*3)+1}`];
        
        if (course) {
          let courseCode = course;
          let section = '';
          
          const match = course.match(/([A-Za-z0-9]+)\(([^)]+)\)/);
          if (match) {
            courseCode = match[1];
            section = match[2];
          }
          
          if (!organizedData[currentDay][timeSlot][roomName]) {
            organizedData[currentDay][timeSlot][roomName] = {
              course: '',
              section: '',
              teacher: ''
            };
          }
          
          organizedData[currentDay][timeSlot][roomName] = {
            course: courseCode,
            section: section,
            teacher: teacher || ''
          };
        }
      }
    }
    
    // Reset state if we encounter another day
    if ((i < items.length - 1) && isDayObject(items[i + 1])) {
      isTimeSlotRow = false;
      isTitleRow = false;
    }
  }
  
  return organizedData;
}

// Function to identify building from room name
function getBuilding(roomName: string): string {
  if (!roomName) return '';
  const parts = roomName.split('-');
  if (parts.length >= 1) {
    return parts[0].trim();
  }
  return '';
}

// Function to extract room number from room name
function getRoomNumber(roomName: string): string {
  if (!roomName) return '';
  const parts = roomName.split('-');
  if (parts.length >= 2) {
    return parts[1].trim().split('\n')[0].split(' ')[0];
  } else {
    return parts[0].trim();
  }
}

// Function to determine if it's a lab or theory class
function getType(roomName: string): string {
  if (!roomName) return 'Theory';
  return roomName.toLowerCase().includes('lab') ? 'Lab' : 'Theory';
}

// Function to apply style to a cell
function styleCellWithBorder(cell: ExcelJS.Cell): void {
  const borderStyle = {
    top: { style: 'thin' as const },
    left: { style: 'thin' as const },
    bottom: { style: 'thin' as const },
    right: { style: 'thin' as const }
  };
  
  cell.border = borderStyle;
  cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
}

// Function to create Excel file from organized data (equivalent to create_excel.js)
export async function createExcelFromData(organizedData: OrganizedRoutineData): Promise<Buffer> {
  const workbook = new ExcelJS.Workbook();
  const worksheet = workbook.addWorksheet('Routine');

  const timeSlots = [
    '08:30-10:00', 
    '10:00-11:30', 
    '11:30-01:00', 
    '01:00-02:30', 
    '02:30-04:00', 
    '04:00-05:30'
  ];

  const days = Object.keys(organizedData);
  
  // Collect all unique rooms across all days and time slots
  const allUniqueRooms = new Set<string>();
  days.forEach(day => {
    timeSlots.forEach(timeSlot => {
      if (organizedData[day][timeSlot]) {
        Object.keys(organizedData[day][timeSlot]).forEach(room => {
          allUniqueRooms.add(room);
        });
      }
    });
  });

  const allRoomsArray = Array.from(allUniqueRooms).sort();
  
  let currentRow = 1;
  const totalColumns = 3 + (timeSlots.length * 3);

  days.forEach(day => {
    // Day name row
    worksheet.getCell(currentRow, 1).value = day;
    worksheet.mergeCells(currentRow, 1, currentRow, totalColumns);
    const dayCell = worksheet.getCell(currentRow, 1);
    dayCell.alignment = { horizontal: 'center', vertical: 'middle' };
    dayCell.font = { bold: true, size: 14 };
    dayCell.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFD3D3D3' }
    };
    styleCellWithBorder(dayCell);
    
    currentRow++;
    
    // Time slots row
    worksheet.getCell(currentRow, 1).value = '';
    worksheet.getCell(currentRow, 2).value = '';
    worksheet.getCell(currentRow, 3).value = '';
    styleCellWithBorder(worksheet.getCell(currentRow, 1));
    styleCellWithBorder(worksheet.getCell(currentRow, 2));
    styleCellWithBorder(worksheet.getCell(currentRow, 3));
    
    let currentColumn = 4;
    timeSlots.forEach(timeSlot => {
      worksheet.getCell(currentRow, currentColumn).value = timeSlot;
      worksheet.mergeCells(currentRow, currentColumn, currentRow, currentColumn + 2);
      const timeCell = worksheet.getCell(currentRow, currentColumn);
      timeCell.alignment = { horizontal: 'center', vertical: 'middle' };
      timeCell.font = { bold: true };
      timeCell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FFEAEAEA' }
      };
      styleCellWithBorder(timeCell);
      currentColumn += 3;
    });
    
    currentRow++;
    
    // Column headers
    worksheet.getCell(currentRow, 1).value = 'Building';
    worksheet.getCell(currentRow, 2).value = 'Room';
    worksheet.getCell(currentRow, 3).value = 'Type';
    styleCellWithBorder(worksheet.getCell(currentRow, 1));
    styleCellWithBorder(worksheet.getCell(currentRow, 2));
    styleCellWithBorder(worksheet.getCell(currentRow, 3));
    
    currentColumn = 4;
    timeSlots.forEach(() => {
      worksheet.getCell(currentRow, currentColumn).value = 'Course';
      worksheet.getCell(currentRow, currentColumn + 1).value = 'Section';
      worksheet.getCell(currentRow, currentColumn + 2).value = 'Teacher';
      
      styleCellWithBorder(worksheet.getCell(currentRow, currentColumn));
      styleCellWithBorder(worksheet.getCell(currentRow, currentColumn + 1));
      styleCellWithBorder(worksheet.getCell(currentRow, currentColumn + 2));
      
      currentColumn += 3;
    });
    
    // Style header row
    for (let i = 1; i <= totalColumns; i++) {
      worksheet.getCell(currentRow, i).font = { bold: true };
      worksheet.getCell(currentRow, i).fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FFF0F0F0' }
      };
    }
    
    currentRow++;
    
    // Data rows
    allRoomsArray.forEach(roomName => {
      const building = getBuilding(roomName);
      const roomNumber = getRoomNumber(roomName);
      const type = getType(roomName);
      
      worksheet.getCell(currentRow, 1).value = building;
      worksheet.getCell(currentRow, 2).value = roomNumber;
      worksheet.getCell(currentRow, 3).value = type;
      styleCellWithBorder(worksheet.getCell(currentRow, 1));
      styleCellWithBorder(worksheet.getCell(currentRow, 2));
      styleCellWithBorder(worksheet.getCell(currentRow, 3));
      
      let dataColumnIndex = 4;
      timeSlots.forEach(timeSlot => {
        const timeSlotData = organizedData[day][timeSlot];
        if (timeSlotData && timeSlotData[roomName]) {
          const classData = timeSlotData[roomName];
          worksheet.getCell(currentRow, dataColumnIndex).value = classData.course || '';
          worksheet.getCell(currentRow, dataColumnIndex + 1).value = classData.section || '';
          worksheet.getCell(currentRow, dataColumnIndex + 2).value = classData.teacher || '';
        } else {
          worksheet.getCell(currentRow, dataColumnIndex).value = '';
          worksheet.getCell(currentRow, dataColumnIndex + 1).value = '';
          worksheet.getCell(currentRow, dataColumnIndex + 2).value = '';
        }
        
        styleCellWithBorder(worksheet.getCell(currentRow, dataColumnIndex));
        styleCellWithBorder(worksheet.getCell(currentRow, dataColumnIndex + 1));
        styleCellWithBorder(worksheet.getCell(currentRow, dataColumnIndex + 2));
        
        dataColumnIndex += 3;
      });
      
      currentRow++;
    });
    
    currentRow++;
  });

  // Set column widths
  worksheet.columns.forEach((column, index) => {
    if (index === 0) {
      column.width = 15;
    } else if (index === 1) {
      column.width = 10;
    } else if (index === 2) {
      column.width = 10;
    } else if ((index - 3) % 3 === 0) {
      column.width = 15;
    } else if ((index - 4) % 3 === 0) {
      column.width = 12;
    } else if ((index - 5) % 3 === 0) {
      column.width = 12;
    }
  });

  return await workbook.xlsx.writeBuffer() as Buffer;
}
