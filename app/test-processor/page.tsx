"use client";

import { useState } from "react";
import { testExcelGeneration } from "@/app/lib/testRoutineProcessor";

export default function TestProcessorPage() {
  const [testResult, setTestResult] = useState<string | null>(null);
  const [isRunning, setIsRunning] = useState(false);

  const runTest = async () => {
    setIsRunning(true);
    setTestResult(null);
    
    try {
      const result = await testExcelGeneration();
      setTestResult(result ? "✅ Test passed! Excel generation works correctly." : "❌ Test failed!");
    } catch (error) {
      setTestResult(`❌ Test failed with error: ${error}`);
    } finally {
      setIsRunning(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-background to-background/95 p-8">
      <div className="max-w-2xl mx-auto">
        <h1 className="text-3xl font-bold mb-8 text-center">
          Routine Processor Test
        </h1>
        
        <div className="bg-card rounded-xl p-6 border border-border/50 shadow-md">
          <p className="text-muted-foreground mb-6">
            This page tests the Excel generation functionality of the routine processor.
          </p>
          
          <button
            onClick={runTest}
            disabled={isRunning}
            className="w-full px-6 py-3 bg-primary text-primary-foreground rounded-lg font-medium hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isRunning ? "Running Test..." : "Run Test"}
          </button>
          
          {testResult && (
            <div className="mt-6 p-4 rounded-lg bg-muted">
              <p className="font-medium">{testResult}</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
