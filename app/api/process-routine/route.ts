import { NextRequest, NextResponse } from 'next/server';
import { parseExcelToJson, transformRoutineData, createExcelFromData } from '@/app/lib/routineProcessor';

export async function POST(request: NextRequest) {
  try {
    // Parse the form data
    const formData = await request.formData();
    const file = formData.get('file') as File;

    if (!file) {
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      );
    }

    // Validate file type
    if (!file.name.endsWith('.xlsx') && file.type !== 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet') {
      return NextResponse.json(
        { error: 'Invalid file type. Please upload an Excel (.xlsx) file.' },
        { status: 400 }
      );
    }

    // Validate file size (10MB limit)
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (file.size > maxSize) {
      return NextResponse.json(
        { error: 'File size too large. Maximum size is 10MB.' },
        { status: 400 }
      );
    }

    // Convert file to buffer
    const arrayBuffer = await file.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);

    // Step 1: Parse Excel to JSON
    let jsonData;
    try {
      jsonData = parseExcelToJson(buffer);
    } catch (error) {
      console.error('Error parsing Excel file:', error);
      return NextResponse.json(
        { error: 'Failed to parse Excel file. Please ensure it\'s a valid Excel file with the expected format.' },
        { status: 400 }
      );
    }

    // Step 2: Transform the data structure
    let organizedData;
    try {
      organizedData = transformRoutineData(jsonData);
    } catch (error) {
      console.error('Error transforming data:', error);
      return NextResponse.json(
        { error: 'Failed to transform data structure. Please ensure the Excel file has the expected routine format.' },
        { status: 400 }
      );
    }

    // Step 3: Create new Excel file
    let excelBuffer;
    try {
      excelBuffer = await createExcelFromData(organizedData);
    } catch (error) {
      console.error('Error creating Excel file:', error);
      return NextResponse.json(
        { error: 'Failed to generate output Excel file.' },
        { status: 500 }
      );
    }

    // Return the Excel file
    return new NextResponse(excelBuffer, {
      status: 200,
      headers: {
        'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'Content-Disposition': 'attachment; filename="processed_routine.xlsx"',
        'Content-Length': excelBuffer.length.toString(),
      },
    });

  } catch (error) {
    console.error('Unexpected error in process-routine API:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred while processing the file.' },
      { status: 500 }
    );
  }
}

// Handle unsupported methods
export async function GET() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}

export async function PUT() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}

export async function DELETE() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}
