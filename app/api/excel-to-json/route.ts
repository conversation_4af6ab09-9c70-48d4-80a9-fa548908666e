import { NextRequest, NextResponse } from 'next/server';
import { processExcelToCompressedJson } from '@/app/lib/excelToJsonProcessor';
import { mkdir } from 'fs/promises';
import { join } from 'path';

export async function POST(request: NextRequest) {
  try {
    // Parse the form data
    const formData = await request.formData();
    const file = formData.get('file') as File;
    const filename = formData.get('filename') as string || 'compressed_routine.json';

    if (!file) {
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      );
    }

    // Validate file type
    if (!file.name.endsWith('.xlsx') && file.type !== 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet') {
      return NextResponse.json(
        { error: 'Invalid file type. Please upload an Excel (.xlsx) file.' },
        { status: 400 }
      );
    }

    // Validate file size (10MB limit)
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (file.size > maxSize) {
      return NextResponse.json(
        { error: 'File size too large. Maximum size is 10MB.' },
        { status: 400 }
      );
    }

    // Convert file to buffer
    const arrayBuffer = await file.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);

    // Ensure public/data directory exists
    const publicDataPath = join(process.cwd(), 'public', 'data');
    try {
      await mkdir(publicDataPath, { recursive: true });
    } catch (error) {
      // Directory might already exist, which is fine
    }

    // Process the Excel file
    let result;
    try {
      result = await processExcelToCompressedJson(buffer, filename);
    } catch (error) {
      console.error('Error processing Excel file:', error);
      return NextResponse.json(
        { 
          error: 'Failed to process Excel file. Please ensure it has the expected routine format.',
          details: error instanceof Error ? error.message : 'Unknown error'
        },
        { status: 400 }
      );
    }

    // Return success response with details
    return NextResponse.json({
      success: true,
      message: 'Excel file successfully converted to compressed JSON format',
      data: {
        totalDays: Object.keys(result.compressedData).length,
        savedFiles: result.savedFiles,
        keyGuide: result.keyGuide,
        preview: {
          days: Object.keys(result.compressedData),
          sampleData: Object.keys(result.compressedData).length > 0 
            ? { [Object.keys(result.compressedData)[0]]: result.compressedData[Object.keys(result.compressedData)[0]] }
            : {}
        }
      }
    });

  } catch (error) {
    console.error('Unexpected error in excel-to-json API:', error);
    return NextResponse.json(
      { 
        error: 'An unexpected error occurred while processing the file.',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// Handle unsupported methods
export async function GET() {
  return NextResponse.json(
    { 
      error: 'Method not allowed',
      message: 'This endpoint only accepts POST requests with file uploads.'
    },
    { status: 405 }
  );
}

export async function PUT() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}

export async function DELETE() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}
