<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Offline - DIU Routine Viewer</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            text-align: center;
            padding: 20px;
        }

        .container {
            max-width: 500px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px 30px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .offline-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 20px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 40px;
        }

        h1 {
            font-size: 2rem;
            margin-bottom: 15px;
            font-weight: 700;
        }

        p {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            opacity: 0.9;
        }

        .features {
            text-align: left;
            margin: 30px 0;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
        }

        .features h3 {
            font-size: 1.2rem;
            margin-bottom: 15px;
            text-align: center;
        }

        .features ul {
            list-style: none;
            padding: 0;
        }

        .features li {
            padding: 8px 0;
            padding-left: 25px;
            position: relative;
            font-size: 0.95rem;
        }

        .features li::before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #4ade80;
            font-weight: bold;
        }

        .retry-btn {
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 12px 30px;
            border-radius: 25px;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            margin-top: 20px;
        }

        .retry-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
            transform: translateY(-2px);
        }

        .status {
            margin-top: 20px;
            padding: 10px;
            border-radius: 10px;
            font-size: 0.9rem;
        }

        .status.online {
            background: rgba(74, 222, 128, 0.2);
            border: 1px solid rgba(74, 222, 128, 0.3);
        }

        .status.offline {
            background: rgba(248, 113, 113, 0.2);
            border: 1px solid rgba(248, 113, 113, 0.3);
        }

        @media (max-width: 480px) {
            .container {
                padding: 30px 20px;
            }
            
            h1 {
                font-size: 1.5rem;
            }
            
            p {
                font-size: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="offline-icon">
            📚
        </div>
        
        <h1>You're Offline</h1>
        <p>Don't worry! DIU Routine Viewer works offline too.</p>
        
        <div class="features">
            <h3>Available Offline Features:</h3>
            <ul>
                <li>View previously searched class schedules</li>
                <li>Browse cached routine data</li>
                <li>Access all main app features</li>
                <li>Dark/light theme switching</li>
                <li>Local data processing tools</li>
            </ul>
        </div>
        
        <div id="connectionStatus" class="status offline">
            🔴 Currently offline
        </div>
        
        <a href="/" class="retry-btn" id="retryBtn">
            Go to App
        </a>
        
        <p style="font-size: 0.9rem; margin-top: 20px; opacity: 0.7;">
            Your data is safely cached and ready to use!
        </p>
    </div>

    <script>
        // Check online status
        function updateConnectionStatus() {
            const statusEl = document.getElementById('connectionStatus');
            const retryBtn = document.getElementById('retryBtn');
            
            if (navigator.onLine) {
                statusEl.textContent = '🟢 Back online!';
                statusEl.className = 'status online';
                retryBtn.textContent = 'Return to App';
            } else {
                statusEl.textContent = '🔴 Currently offline';
                statusEl.className = 'status offline';
                retryBtn.textContent = 'Go to App';
            }
        }

        // Listen for online/offline events
        window.addEventListener('online', updateConnectionStatus);
        window.addEventListener('offline', updateConnectionStatus);
        
        // Initial status check
        updateConnectionStatus();
        
        // Auto-redirect when back online (optional)
        window.addEventListener('online', () => {
            setTimeout(() => {
                if (confirm('You\'re back online! Would you like to return to the app?')) {
                    window.location.href = '/';
                }
            }, 2000);
        });
    </script>
</body>
</html>
