// Service Worker for DIU Routine Viewer

const CACHE_NAME = 'diu-routine-viewer-v2';
const OFFLINE_URL = '/offline.html';
const DATA_CACHE_NAME = 'diu-routine-data-v1';

const urlsToCache = [
  '/',
  '/offline.html',
  '/favicon.ico',
  '/icon.svg',
  '/apple-touch-icon.png',
  '/og-image.png',
  '/twitter-image.png',
  '/manifest.json',
  '/icons/icon-192x192.png',
  '/icons/icon-512x512.png',
  '/setup',
  '/data-converter',
  '/test-processor'
];

// Critical data files that should be cached for offline use
const dataFilesToCache = [
  '/data/compressed_routine.json',
  '/data/compressed_routine_readable.json',
  '/data/key_guide.json'
];

// Install event - cache assets
self.addEventListener('install', (event) => {
  event.waitUntil(
    Promise.all([
      // Cache static assets
      caches.open(CACHE_NAME)
        .then((cache) => {
          console.log('Opened static cache');
          return cache.addAll(urlsToCache);
        }),
      // Cache data files
      caches.open(DATA_CACHE_NAME)
        .then((cache) => {
          console.log('Opened data cache');
          return cache.addAll(dataFilesToCache);
        })
    ]).then(() => self.skipWaiting())
  );
});

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
  event.waitUntil(
    caches.keys().then((cacheNames) => {
      return Promise.all(
        cacheNames.map((cacheName) => {
          if (cacheName !== CACHE_NAME && cacheName !== DATA_CACHE_NAME) {
            console.log('Deleting old cache:', cacheName);
            return caches.delete(cacheName);
          }
        })
      );
    }).then(() => self.clients.claim())
  );
});

// Fetch event - serve from cache or network
self.addEventListener('fetch', (event) => {
  // Skip cross-origin requests
  if (!event.request.url.startsWith(self.location.origin)) {
    return;
  }

  const url = new URL(event.request.url);

  // Handle data files with cache-first strategy
  if (url.pathname.startsWith('/data/')) {
    event.respondWith(
      caches.match(event.request, { cacheName: DATA_CACHE_NAME })
        .then((cachedResponse) => {
          if (cachedResponse) {
            console.log('Serving data from cache:', url.pathname);
            return cachedResponse;
          }

          // If not in cache, try network and cache the response
          return fetch(event.request)
            .then((response) => {
              if (response.status === 200) {
                const responseClone = response.clone();
                caches.open(DATA_CACHE_NAME).then((cache) => {
                  cache.put(event.request, responseClone);
                });
              }
              return response;
            })
            .catch(() => {
              console.log('Data file not available offline:', url.pathname);
              return new Response('Data not available offline', {
                status: 503,
                headers: { 'Content-Type': 'text/plain' }
              });
            });
        })
    );
    return;
  }

  // Handle API requests
  if (url.pathname.startsWith('/api/')) {
    event.respondWith(
      fetch(event.request)
        .catch(() => {
          console.log('API request failed, serving offline page');
          return caches.match(OFFLINE_URL);
        })
    );
    return;
  }

  // Handle navigation requests (pages)
  if (event.request.mode === 'navigate') {
    event.respondWith(
      fetch(event.request)
        .then((response) => {
          // Cache successful page responses
          if (response.status === 200) {
            const responseClone = response.clone();
            caches.open(CACHE_NAME).then((cache) => {
              cache.put(event.request, responseClone);
            });
          }
          return response;
        })
        .catch(() => {
          // If network fails, try cache first, then offline page
          return caches.match(event.request)
            .then((cachedResponse) => {
              if (cachedResponse) {
                console.log('Serving page from cache:', url.pathname);
                return cachedResponse;
              }
              console.log('Serving offline page for:', url.pathname);
              return caches.match(OFFLINE_URL);
            });
        })
    );
    return;
  }

  // For other requests (assets, etc.), try cache first, then network
  event.respondWith(
    caches.match(event.request)
      .then((cachedResponse) => {
        if (cachedResponse) {
          return cachedResponse;
        }

        return fetch(event.request)
          .then((response) => {
            // Cache successful responses
            if (response.status === 200) {
              const responseClone = response.clone();
              caches.open(CACHE_NAME).then((cache) => {
                cache.put(event.request, responseClone);
              });
            }
            return response;
          })
          .catch(() => {
            console.log('Asset not available:', url.pathname);
            return new Response('Resource not available offline', {
              status: 503,
              headers: { 'Content-Type': 'text/plain' }
            });
          });
      })
  );
});